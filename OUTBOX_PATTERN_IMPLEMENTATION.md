# Outbox Pattern Implementation for ATMA Backend

## Overview

This implementation solves the synchronization issue between database writes and RabbitMQ publishing by implementing the Outbox Pattern. The pattern ensures that assessment creation and event publishing are atomic operations, preventing data inconsistency.

## Problem Solved

**Before**: Assessment creation (database write) and job publishing (RabbitMQ) were separate operations that could fail independently, causing data inconsistency.

**After**: Assessment data and outbox events are written in a single database transaction, with a background worker ensuring eventual delivery to RabbitMQ.

## Architecture Changes

```
OLD FLOW:
Assessment Service → Archive Service (DB) → RabbitMQ → Analysis Worker
                                    ↑
                              Potential failure point

NEW FLOW:
Assessment Service → Archive Service (DB + Outbox) → Outbox Publisher → RabbitMQ → Analysis Worker
                                                            ↑
                                                    Reliable delivery
```

## Database Queries to Run

You mentioned you already ran the outbox table creation. Here are any additional queries you might need:

### 1. Verify Outbox Table Exists
```sql
-- Check if outbox table exists and has correct structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'archive' 
  AND table_name = 'outbox_events'
ORDER BY ordinal_position;
```

### 2. Verify Indexes (Optional - for performance)
```sql
-- Check if indexes exist
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE schemaname = 'archive' 
  AND tablename = 'outbox_events';
```

### 3. Grant Permissions (if needed)
```sql
-- Grant permissions to your application user
GRANT SELECT, INSERT, UPDATE ON archive.outbox_events TO your_app_user;
GRANT USAGE ON SCHEMA archive TO your_app_user;
```

## Implementation Summary

### Phase 1: Archive Service - Outbox Infrastructure ✅
- ✅ Created `OutboxEvent` Sequelize model
- ✅ Created `OutboxService` with CRUD operations
- ✅ Added outbox management routes (`/outbox/*`)
- ✅ Modified job creation to include outbox events in same transaction
- ✅ Added new endpoint `/jobs/with-outbox` for atomic operations

### Phase 2: Assessment Service - Remove Direct Publishing ✅
- ✅ Added `createJobWithOutboxEvent()` method to archiveService
- ✅ Modified `/submit` route to use outbox pattern
- ✅ Modified `/test` route to use outbox pattern
- ✅ Updated error handling for outbox operations

### Phase 3: Outbox Publisher Worker ✅
- ✅ Created standalone service with complete structure
- ✅ Implemented core outbox processing logic with polling and batching
- ✅ Created RabbitMQ publisher with proper error handling
- ✅ Added Archive Service client for API communication
- ✅ Implemented main worker with graceful shutdown and monitoring

### Phase 4: Analysis Worker Compatibility ✅
- ✅ Verified message format compatibility
- ✅ Created compatibility verification script
- ✅ Confirmed no changes needed to Analysis Worker

## Files Created/Modified

### Archive Service
- **New**: `src/models/OutboxEvent.js` - Outbox event model
- **New**: `src/services/outboxService.js` - Outbox operations
- **New**: `src/routes/outbox.js` - Outbox management API
- **Modified**: `src/models/index.js` - Added OutboxEvent model
- **Modified**: `src/app.js` - Added outbox routes
- **Modified**: `src/services/analysisJobsService.js` - Added outbox event creation
- **Modified**: `src/routes/results.js` - Added `/jobs/with-outbox` endpoint

### Assessment Service
- **Modified**: `src/services/archiveService.js` - Added `createJobWithOutboxEvent()`
- **Modified**: `src/routes/assessments.js` - Updated to use outbox pattern
- **Modified**: `src/routes/test.js` - Updated to use outbox pattern

### Outbox Publisher (New Service)
- **New**: Complete standalone service in `outbox-publisher/` directory
- **New**: `package.json` - Dependencies and scripts
- **New**: `.env.example` - Configuration template
- **New**: `src/worker.js` - Main entry point
- **New**: `src/config/database.js` - Database configuration
- **New**: `src/config/rabbitmq.js` - RabbitMQ configuration
- **New**: `src/models/OutboxEvent.js` - Read-only outbox model
- **New**: `src/services/outboxProcessor.js` - Core processing logic
- **New**: `src/services/rabbitmqPublisher.js` - RabbitMQ publishing
- **New**: `src/services/archiveServiceClient.js` - Archive Service API client
- **New**: `src/utils/logger.js` - Structured logging
- **New**: `README.md` - Complete documentation
- **New**: `scripts/verify-compatibility.js` - Compatibility verification

## Deployment Instructions

### 1. Deploy Archive Service Changes
```bash
cd archive-service
npm install  # Install any new dependencies
npm restart  # Restart the service
```

### 2. Deploy Assessment Service Changes
```bash
cd assessment-service
npm restart  # Restart the service
```

### 3. Deploy Outbox Publisher
```bash
cd outbox-publisher
npm install
cp .env.example .env
# Edit .env with your configuration
npm start
```

### 4. Verify Deployment
```bash
# Check Archive Service health
curl http://localhost:3002/health

# Check Outbox Publisher health
curl http://localhost:3003/health

# Check outbox events are being created
curl -H "X-Service-Auth: your-api-key" http://localhost:3002/outbox/stats
```

## Configuration

### Environment Variables for Outbox Publisher
```env
# Database (same as Archive Service)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_archive
DB_USER=postgres
DB_PASSWORD=password
DB_SCHEMA=archive

# RabbitMQ (same as existing services)
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_EXCHANGE=atma_events
RABBITMQ_ROUTING_KEY=assessment.analysis

# Processing Configuration
OUTBOX_POLL_INTERVAL=5000
OUTBOX_BATCH_SIZE=50
OUTBOX_MAX_RETRIES=5

# Archive Service API
ARCHIVE_SERVICE_URL=http://localhost:3002
ARCHIVE_SERVICE_API_KEY=your-service-api-key

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PORT=3003
```

## Monitoring

### Health Checks
- **Archive Service**: `GET /health`
- **Outbox Publisher**: `GET /health`
- **Outbox Stats**: `GET /outbox/stats`
- **Outbox Health**: `GET /outbox/health`

### Key Metrics to Monitor
- Number of pending outbox events
- Processing rate (events/second)
- Failure rate
- Oldest unprocessed event age
- RabbitMQ connection status

### Database Monitoring
```sql
-- Monitor outbox event status
SELECT 
  COUNT(*) as total_events,
  COUNT(CASE WHEN processed_at IS NOT NULL THEN 1 END) as processed,
  COUNT(CASE WHEN processed_at IS NULL AND failed_at IS NULL THEN 1 END) as pending,
  COUNT(CASE WHEN failed_at IS NOT NULL THEN 1 END) as failed,
  MIN(created_at) as oldest_event,
  MAX(created_at) as newest_event
FROM archive.outbox_events;
```

## Testing

### 1. Test Assessment Submission
```bash
# Submit a test assessment (should create outbox event)
curl -X POST http://localhost:3001/api/assessments/submit \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "riasec": {...},
    "ocean": {...},
    "viaIs": {...}
  }'
```

### 2. Verify Outbox Event Creation
```sql
-- Check latest outbox events
SELECT id, event_type, aggregate_id, created_at, processed_at 
FROM archive.outbox_events 
ORDER BY created_at DESC 
LIMIT 10;
```

### 3. Test Compatibility
```bash
cd outbox-publisher
node scripts/verify-compatibility.js
```

## Rollback Plan

If issues arise:

1. **Keep Outbox Publisher running** (it will process any existing events)
2. **Temporarily re-enable direct publishing** in Assessment Service
3. **Gradually migrate back** once issues are resolved

The implementation maintains backward compatibility and doesn't break existing functionality.

## Benefits Achieved

✅ **Consistency**: Database writes and message publishing are now atomic
✅ **Reliability**: Messages will eventually be delivered even if RabbitMQ is down
✅ **Observability**: Clear audit trail of all events and their processing status
✅ **Scalability**: Can handle high loads with batching and concurrent processing
✅ **Maintainability**: Clear separation of concerns between services

## Next Steps

1. Monitor the system for 24-48 hours
2. Verify all assessments are being processed correctly
3. Check outbox event processing rates and adjust configuration if needed
4. Set up alerts for high failure rates or processing delays
5. Consider implementing cleanup jobs for old processed events

The Outbox Pattern implementation is now complete and ready for production use!
